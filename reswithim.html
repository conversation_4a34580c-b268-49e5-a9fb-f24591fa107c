<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><PERSON><PERSON> // Information Security Professional</title>
    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Roboto+Mono:wght@400;500&family=Share+Tech+Mono&display=swap" rel="stylesheet">
    <!-- Font Awesome for Icons -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <!-- Croppie Library CSS -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.5/croppie.min.css">
    <style>
      /* "Blueprint Precision" Theme */
      :root {
        --font-heading: 'Share Tech Mono', monospace;
        --font-body: 'Roboto Mono', monospace;
        /* BACKGROUNDS (Subtle grays for a layered look) */
        --color-bg-dark: #E5E7EB;
        /* Light Silver/Gray */
        --color-bg-medium: #FFFFFF;
        /* Pure White */
        /* TEXT & HIGHLIGHTS (High-contrast and sharp) */
        --color-primary-glow: #1D4ED8;
        /* Deep, professional Blue */
        --color-secondary-glow: #3B82F6;
        /* Brighter accent Blue */
        --color-text: #1F2937;
        /* Dark Charcoal (almost black) */
        /* ACCENT & BORDERS */
        --color-accent: #1D4ED8;
        /* Matching the primary blue */
        /* SHADOWS (Clean, subtle drop shadows) */
        --page-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
        --text-glow: none;
        /* Glows don't work well on light backgrounds */
      }

      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: var(--font-body);
        background-color: var(--color-bg-dark);
        color: var(--color-text);
        line-height: 1.6;
        font-size: 11pt;
      }

      .page-container {
        display: flex;
        flex-direction: column;
        align-items: center;
        padding: 20px 0;
        gap: 20px;
      }

      .page {
        background: var(--color-bg-medium);
        border: 1px solid #dee2e6;
        width: 210mm;
        height: 297mm;
        padding: 12mm;
        box-shadow: var(--page-shadow);
        display: flex;
        flex-direction: column;
        overflow: hidden;
      }

      h1,
      h2,
      h3 {
        font-family: var(--font-heading);
        color: var(--color-primary-glow);
        text-shadow: var(--text-glow);
      }

      h1 {
        font-size: 24pt;
      }

      h2 {
        font-size: 16pt;
        border-bottom: 2px solid var(--color-accent);
        padding-bottom: 5px;
        margin-top: 15px;
        margin-bottom: 10px;
        display: flex;
        align-items: center;
        gap: 12px;
      }

      h3 {
        font-size: 14pt;
        color: var(--color-secondary-glow);
      }

      ul {
        list-style: none;
      }

      ul li {
        position: relative;
        padding-left: 20px;
        margin-bottom: 5px;
      }

      ul li::before {
        content: '>';
        position: absolute;
        left: 0;
        top: 1px;
        color: var(--color-accent);
        font-weight: bold;
      }

      a {
        color: var(--color-secondary-glow);
        text-decoration: none;
      }

      a:hover {
        text-decoration: underline;
        color: var(--color-primary-glow);
      }

      strong {
        font-weight: 700;
        color: var(--color-accent);
      }

      .header {
        display: flex;
        align-items: center;
        gap: 20px;
        border-bottom: 1px solid #e9ecef;
        padding-bottom: 12px;
        margin-bottom: 12px;
      }

      .profile-photo-container {
        position: relative;
        cursor: pointer;
      }

      .profile-photo-container .profile-img {
        width: 90px;
        height: 90px;
        border-radius: 50%;
        border: 2px solid var(--color-accent);
        background: #f8f9fa;
        background-size: cover;
        background-position: center top;
        background-repeat: no-repeat;
      }

      .profile-photo-container .upload-icon {
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: var(--color-text);
        font-size: 24px;
        opacity: 0;
        transition: opacity 0.3s ease;
      }

      .profile-photo-container:hover .upload-icon {
        opacity: 0.8;
      }

      #imageUpload {
        display: none;
      }

      .header-text {
        flex-grow: 1;
      }

      .job-title-header {
        font-size: 13pt;
        color: var(--color-text);
        margin: 2px 0 8px 0;
      }

      .contact-info {
        display: flex;
        flex-wrap: wrap;
        gap: 6px 15px;
        font-size: 10pt;
      }

      .contact-info a {
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .contact-info .fa-solid,
      .contact-info .fa-brands {
        color: var(--color-accent);
      }

      .experience-item {
        padding: 8px 12px;
        margin-bottom: 10px;
        border-left: 3px solid var(--color-accent);
        background-color: #f8f9fa;
      }

      .job-header {
        display: flex;
        justify-content: space-between;
        align-items: flex-start;
        margin-bottom: 3px;
        gap: 15px;
      }

      .job-title {
        font-family: var(--font-body);
        font-weight: 500;
        font-size: 12pt;
        color: var(--color-primary-glow);
      }

      .job-company {
        font-weight: 500;
        color: var(--color-text);
        font-size: 11pt;
      }

      .job-dates {
        font-size: 10pt;
        color: #6c757d;
        white-space: nowrap;
        font-style: italic;
      }

      .skills-grid {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 2px 15px;
      }

      /* FIX: Technical Arsenal Section Styling */
      .skills-container {
        display: grid;
        grid-template-columns: repeat(2, 1fr);
        gap: 12px;
      }

      .skill-category {
        background: #f1f5f9 !important;
        var(--color-text);
        /* FIX: Set to dark charcoal from theme */
        padding: 12px;
        border-radius: 4px;
      }

      .skill-category h4 {
        font-size: 12pt;
        margin-bottom: 12px;
        color: var(--color-bg-medium);
        /* FIX: Invert text to white */
        display: flex;
        align-items: center;
        gap: 8px;
      }

      .skill-pills {
        display: flex;
        flex-wrap: wrap;
        gap: 8px;
      }

      .skill-pill {
        padding: 4px 12px;
        border: 1px solid var(--color-secondary-glow);
        color: var(--color-secondary-glow);
        /* FIX: Use blue for text */
        border-radius: 20px;
        font-size: 9pt;
        background-color: transparent;
        transition: all 0.2s ease-in-out;
      }

      .skill-pill:hover {
        background-color: var(--color-accent);
        color: var(--color-bg-medium);
        border-color: var(--color-accent);
        transform: translateY(-2px);
        box-shadow: 0 4px 10px rgba(29, 78, 216, 0.2);
      }

      /* FIX: Bottom space fix */
      .edu-cert-container {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 20px;
        margin-top: auto;
      }

      .education-item li,
      .certification-item li {
        margin-bottom: 8px;
      }

      .print-button {
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: var(--color-accent);
        color: #fff;
        border: none;
        padding: 10px 20px;
        border-radius: 5px;
        font-family: var(--font-heading);
        font-size: 14px;
        cursor: pointer;
        z-index: 1000;
        box-shadow: var(--page-shadow);
      }

      .print-button:hover {
        background-color: var(--color-secondary-glow);
      }

      /* CSS FOR MODAL AND CROPPIE */
      .crop-modal {
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background: rgba(0, 0, 0, 0.7);
        display: flex;
        justify-content: center;
        align-items: center;
        z-index: 2000;
        opacity: 0;
        visibility: hidden;
        transition: opacity 0.3s, visibility 0.3s;
      }

      .crop-modal.visible {
        opacity: 1;
        visibility: visible;
      }

      .crop-container {
        background: var(--color-bg-medium);
        padding: 25px;
        border-radius: 5px;
        box-shadow: var(--page-shadow);
      }

      .crop-save-button {
        display: block;
        width: 100%;
        margin-top: 20px;
        background-color: var(--color-accent);
        color: #fff;
        border: none;
        padding: 12px 20px;
        font-family: var(--font-heading);
        font-size: 14px;
        cursor: pointer;
      }

      .cr-slider-wrap {
        margin-top: 15px;
      }

      @media print {
        body {
          font-size: 9pt;
          background: #fff;
        }

        .page-container {
          padding: 0;
          gap: 0;
        }

        .page {
          box-shadow: none;
          border: none;
          width: 100%;
          height: auto;
          overflow: visible;
          padding: 0;
        }

        .print-button,
        .upload-icon,
        .crop-modal {
          display: none;
        }

        .experience-item,
        .skill-category {
          page-break-inside: avoid;
        }

        .skill-category {
          background: #f1f5f9 !important;
        }

        .skill-pill {
          color: var(--color-text) !important;
          border-color: #cbd5e1 !important;
        }

        h1,
        h2,
        h3,
        h4,
        a,
        strong,
        .job-title {
          color: var(--color-text) !important;
        }

        @page {
          size: A4;
          margin: 12mm;
        }
      }
    </style>
  </head>
  <body>
    <button id="printBtn" class="print-button">
      <i class="fa-solid fa-print"></i> Print / Save as PDF </button>
    <div class="page-container">
      <!-- PAGE 1 -->
      <div class="page" id="page1">
        <header class="header">
          <label for="imageUpload" class="profile-photo-container">
            <div class="profile-img" id="profileImage"></div>
            <div class="upload-icon">
              <i class="fa-solid fa-camera"></i>
            </div>
          </label>
          <input type="file" id="imageUpload" accept="image/*">
          <div class="header-text">
            <h1>RAHUL SIMON</h1>
            <p class="job-title-header">Information Security Professional</p>
            <div class="contact-info">
              <a href="tel:+917507333583">
                <i class="fa-solid fa-phone-volume"></i> +917507333583 </a>
              <a href="mailto:<EMAIL>">
                <i class="fa-solid fa-envelope"></i> <EMAIL> </a>
            </div>
          </div>
        </header>
        <section id="summary">
          <h2>
            <i class="fa-solid fa-terminal"></i> PROFESSIONAL SUMMARY
          </h2>
          <p>Dynamic cybersecurity professional with over five years of specialized experience in security assessments, comprehensive audits, and strategic consulting within the high-stakes BFSI, Financial Services, and Telecom sectors. Demonstrates a proven ability to lead critical security initiatives, achieving a significant 30% reduction in application vulnerabilities and ensuring unwavering 100% regulatory compliance. Adept at collaborating with cross-functional teams to integrate security into the deployment lifecycle, thereby minimizing operational risks.</p>
        </section>
        <section id="experience">
          <h2>
            <i class="fa-solid fa-network-wired"></i> PROFESSIONAL EXPERIENCE
          </h2>
          <article class="experience-item">
            <div class="job-header">
              <div>
                <h3 class="job-title">Information Security Consultant</h3>
                <div class="job-company">Fintech Startup</div>
              </div>
              <div class="job-dates">Dec 2023 - Present</div>
            </div>
            <ul>
              <li>Spearheaded threat modeling and security assessment initiatives for flagship products, delivering strategic architectural recommendations that resulted in a tangible <strong>25% reduction</strong> in security risks across distributed financial platforms. </li>
              <li>Engineered and deployed a robust set of security controls fully aligned with stringent financial industry regulations (RBI, SEBI), leading to a successful and seamless <strong>100% compliance audit</strong> for ISO 27001 standard. </li>
            </ul>
          </article>
          <article class="experience-item">
            <div class="job-header">
              <div>
                <h3 class="job-title">Senior Executive, Internal Audit</h3>
                <div class="job-company">Cognizant</div>
              </div>
              <div class="job-dates">May 2022 - Jul 2023</div>
            </div>
            <ul>
              <li>Executed in-depth IT controls design reviews and operational effectiveness assessments across global business units, consistently achieving <strong>100% compliance</strong> with SOX and other regulatory mandates. </li>
              <li>Directed specialized IT audits focused on cloud infrastructure and CI/CD pipeline security, which culminated in a <strong>30% measurable improvement</strong> in the overall infrastructure security posture. </li>
            </ul>
          </article>
          <article class="experience-item">
            <div class="job-header">
              <div>
                <h3 class="job-title">Asst. Manager, Cybersecurity Services</h3>
                <div class="job-company">Deloitte</div>
              </div>
              <div class="job-dates">Mar 2020 - Nov 2020</div>
            </div>
            <ul>
              <li>Led Cyber Maturity Assessments for Fortune 500 clients, developing multi-year strategic roadmaps that demonstrably improved their cybersecurity posture by an average of <strong>40%</strong>. </li>
              <li>Acted as the lead consultant for a major manufacturing enterprise's ISO 27001 implementation, conducting full-scope gap assessments and policy reviews to ensure <strong>100% certification compliance</strong> on the first attempt. </li>
            </ul>
          </article>
        </section>
      </div>
      <!-- PAGE 2 -->
      <div class="page" id="page2">
        <article class="experience-item">
          <div class="job-header">
            <div>
              <h3 class="job-title">Consultant / Associate Consultant</h3>
              <div class="job-company">KPMG</div>
            </div>
            <div class="job-dates">Apr 2017 - Feb 2020</div>
          </div>
          <ul>
            <li>Managed and delivered comprehensive VAPT and web application security reviews for India’s largest credit rating company, successfully identifying and remediating flaws to reduce critical vulnerabilities by <strong>30%</strong>. </li>
            <li>Orchestrated multiple cybersecurity implementation projects for a large wholesale bank in Bahrain, including vendor selection (SIEM, DAM) and regulatory compliance, resulting in a <strong>40% faster audit closure</strong> cycle. </li>
          </ul>
        </article>
        <section id="competencies">
          <h2>
            <i class="fa-solid fa-gears"></i> CORE COMPETENCIES
          </h2>
          <ul class="skills-grid">
            <li>Vulnerability Assessment</li>
            <li>Penetration Testing (VAPT)</li>
            <li>Web Application Security</li>
            <li>InfoSec Governance</li>
            <li>ISO 27001 Implementation</li>
            <li>IT General Controls (ITGC)</li>
            <li>Secure Configuration Review</li>
            <li>Third-Party Risk Management</li>
            <li>Cyber Maturity Assessment</li>
            <li>Security Policy Development</li>
          </ul>
        </section>
        <section id="technical-skills">
          <h2>
            <i class="fa-solid fa-laptop-code"></i> TECHNICAL ARSENAL
          </h2>
          <div class="skills-container">
            <div class="skill-category">
              <h4>
                <i class="fa-solid fa-bug-slash"></i> Offensive Security Tools
              </h4>
              <div class="skill-pills">
                <span class="skill-pill">Nessus</span>
                <span class="skill-pill">Burp Suite Pro</span>
                <span class="skill-pill">OWASP ZAP</span>
                <span class="skill-pill">Metasploit</span>
              </div>
            </div>
            <div class="skill-category">
              <h4>
                <i class="fa-solid fa-book-atlas"></i> GRC Frameworks
              </h4>
              <div class="skill-pills">
                <span class="skill-pill">ISO 27001</span>
                <span class="skill-pill">NIST CSF</span>
                <span class="skill-pill">GDPR</span>
                <span class="skill-pill">SOX (ITGC)</span>
              </div>
            </div>
            <div class="skill-category">
              <h4>
                <i class="fa-solid fa-file-code"></i> Scripting & Automation
              </h4>
              <div class="skill-pills">
                <span class="skill-pill">Excel Macros (VBA)</span>
                <span class="skill-pill">Basic Python/Bash</span>
                <span class="skill-pill">AI Prompt Engineering</span>
              </div>
            </div>
            <div class="skill-category">
              <h4>
                <i class="fa-solid fa-folder-tree"></i> Management & Docs
              </h4>
              <div class="skill-pills">
                <span class="skill-pill">MS Office Suite</span>
                <span class="skill-pill">TeamMate Audit</span>
                <span class="skill-pill">Project Management</span>
              </div>
            </div>
          </div>
        </section>
        <div class="edu-cert-container">
          <section id="certifications">
            <h2>
              <i class="fa-solid fa-shield-halved"></i> CERTIFICATIONS
            </h2>
            <ul class="certification-item">
              <li>ISO 27001:2013 Lead Auditor</li>
              <li>ISO 20000:2018 Lead Auditor</li>
              <li>ISO 22301:2019 Lead Auditor</li>
            </ul>
          </section>
          <section id="education">
            <h2>
              <i class="fa-solid fa-user-graduate"></i> EDUCATION
            </h2>
            <ul class="education-item">
              <li>
                <strong>MBA (IT Business Mgmt)</strong>
                <br>
                <small>Symbiosis Intl. University (2017)</small>
              </li>
              <li style="margin-top: 10px;">
                <strong>Bachelor of Comp. Apps (BCA)</strong>
                <br>
                <small>Bangalore University (2014)</small>
              </li>
            </ul>
          </section>
        </div>
      </div>
    </div>
    <!-- MODAL HTML -->
    <div id="cropModal" class="crop-modal">
      <div class="crop-container">
        <div id="croppie-editor"></div>
        <button id="cropSaveButton" class="crop-save-button">SET PROFILE IMAGE</button>
      </div>
    </div>
    <!-- Croppie Library JS -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/croppie/2.6.5/croppie.min.js"></script>
    <!-- SCRIPT WITH CROPPIE LOGIC -->
    <script>
      const imageUpload = document.getElementById('imageUpload');
      const profileImage = document.getElementById('profileImage');
      const cropModal = document.getElementById('cropModal');
      const croppieEditor = document.getElementById('croppie-editor');
      const cropSaveButton = document.getElementById('cropSaveButton');
      let croppieInstance = null;
      imageUpload.addEventListener('change', function(event) {
        if (event.target.files && event.target.files[0]) {
          const reader = new FileReader();
          reader.onload = function(e) {
            cropModal.classList.add('visible');
            if (croppieInstance) {
              croppieInstance.destroy();
            }
            croppieInstance = new Croppie(croppieEditor, {
              viewport: {
                width: 200,
                height: 200,
                type: 'circle'
              },
              boundary: {
                width: 300,
                height: 300
              },
              enableExif: true
            });
            croppieInstance.bind({
              url: e.target.result
            });
          }
          reader.readAsDataURL(event.target.files[0]);
        }
      });
      cropSaveButton.addEventListener('click', function() {
        croppieInstance.result({
          type: 'canvas',
          size: 'viewport',
          format: 'png'
        }).then(function(result) {
          profileImage.style.backgroundImage = `url(${result})`;
          cropModal.classList.remove('visible');
        });
      });
      document.getElementById('printBtn').addEventListener('click', function() {
        window.print();
      });
    </script>
  </body>
</html>